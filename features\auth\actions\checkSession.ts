'use server';

import { cookies } from 'next/headers';
import { get } from '@/lib/api';

export interface SessionInfo {
  isValid: boolean;
  user?: {
    id: string;
    name: string;
    email: string;
    role: string;
    tenant_id: string;
  };
  expiresAt?: string;
}

/**
 * Verifica si la sesión actual es válida
 */
export async function checkSession(): Promise<SessionInfo> {
  try {
    const token = cookies().get('auth_token')?.value;
    const tenantId = cookies().get('tenant_id')?.value;

    if (!token) {
      return { isValid: false };
    }

    // Intentar hacer una request a un endpoint protegido para verificar el token
    try {
      const response = await get<any>('/auth/verify'); // Asumiendo que existe este endpoint
      
      return {
        isValid: true,
        user: response.user,
        expiresAt: response.expiresAt
      };
    } catch (error) {
      // Si el endpoint /auth/verify no existe, intentamos con /patient para verificar
      try {
        await get<any>('/patient?page=1&pageSize=1');
        
        // Si llegamos aquí, el token es válido
        return {
          isValid: true,
          user: {
            id: 'unknown',
            name: '<PERSON><PERSON><PERSON>',
            email: 'unknown',
            role: 'physician',
            tenant_id: tenantId || 'unknown'
          }
        };
      } catch (apiError) {
        console.error('Session verification failed:', apiError);
        return { isValid: false };
      }
    }
  } catch (error) {
    console.error('Error checking session:', error);
    return { isValid: false };
  }
}

/**
 * Obtiene información detallada de la sesión para debugging
 */
export async function getSessionDebugInfo() {
  const token = cookies().get('auth_token')?.value;
  const tenantId = cookies().get('tenant_id')?.value;
  
  return {
    hasToken: !!token,
    tokenLength: token?.length || 0,
    hasTenantId: !!tenantId,
    tenantId: tenantId,
    // No incluimos el token completo por seguridad
    tokenPreview: token ? `${token.substring(0, 20)}...` : null
  };
}

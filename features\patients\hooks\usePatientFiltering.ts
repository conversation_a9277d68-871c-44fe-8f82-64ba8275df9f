import { useMemo } from 'react';
import {
  SortCriteria,
  SortDirection,
  DashboardPatient
} from '../types/dashboardTypes';

interface UsePatientFilteringProps {
  patients: DashboardPatient[];
  searchTerm: string;
  sortBy: SortCriteria;
  sortDirection: SortDirection;
}

/**
 * Custom hook to handle filtering and sorting of patients
 */
export function usePatientFiltering({
  patients,
  searchTerm,
  sortBy,
  sortDirection
}: UsePatientFilteringProps) {
  // Filter and sort patients based on search term and sort criteria
  const filteredAndSortedPatients = useMemo(() => {
    // First filter by search term
    const filtered = patients.filter((patient) =>
      patient.name.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Then sort based on the selected criteria
    return [...filtered].sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'age-asc':
          // Convert birthdate strings to Date objects for comparison
          const dateA = new Date(a.birthdate);
          const dateB = new Date(b.birthdate);
          comparison = dateB.getTime() - dateA.getTime(); // Older people (earlier birthdates) first
          break;
        case 'age-desc':
          // Convert birthdate strings to Date objects for comparison
          const dateC = new Date(a.birthdate);
          const dateD = new Date(b.birthdate);
          comparison = dateC.getTime() - dateD.getTime(); // Younger people (later birthdates) first
          break;
        case 'recent':
          // For demo purposes, we'll just use the ID as a proxy for recency
          comparison = parseInt(b.id) - parseInt(a.id);
          break;
        default:
          comparison = a.name.localeCompare(b.name);
      }

      return sortDirection === 'asc' ? comparison : -comparison;
    });
  }, [patients, searchTerm, sortBy, sortDirection]);

  return { filteredAndSortedPatients };
}

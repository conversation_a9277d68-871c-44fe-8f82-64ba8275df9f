'use server';

import { z } from 'zod';
import { post } from '@/lib/api'; // Corrected path alias
import { MedicalPatientDto } from '../types/patient'; // Added import

// Define the schema for the nested structure matching the component's payload
const userSchema = z.object({
  name: z.string().min(2, 'El nombre debe tener al menos 2 caracteres'),
  last_name: z.string().min(2, 'El apellido debe tener al menos 2 caracteres'), // Adjusted min length based on component schema
  email: z.string().email('Correo electrónico inválido'),
  dni: z.string().min(7, 'El DNI debe tener al menos 7 caracteres').max(9),
  birth_date: z.string(), // Expecting ISO string from component
  nationality: z.string(),
  gender: z.string(),
  phone_prefix: z.string(),
  phone: z.string().min(8, 'El teléfono debe tener al menos 8 dígitos.'), // Adjusted min length based on component schema
  image: z.string().nullable().optional() // Match component logic
});

const patientDetailsSchema = z.object({
  direction: z.string(),
  country: z.string(),
  province: z.string(),
  city: z.string(),
  postal_code: z.string(),
  direction_number: z.string().nullable().optional(), // Match component logic
  apartment: z.string().nullable().optional(), // Match component logic
  health_care_number: z.string().nullable().optional() // Match component logic
});

const createPatientPayloadSchema = z.object({
  user: userSchema,
  patient: patientDetailsSchema
  // tenant_id might be added here if needed at the root by the backend
  // If tenant_id is required by the backend at the root level, add it here:
  // tenant_id: z.string().default('1') // Example if needed
});

export type CreatePatientPayload = z.infer<typeof createPatientPayloadSchema>;

export async function createPatient(payload: CreatePatientPayload) {
  try {
    // Validate the incoming payload against the nested schema (optional but good practice)
    // This ensures the data structure is correct before sending to the API
    const validatedPayload = createPatientPayloadSchema.parse(payload);

    // Add explicit logging before sending to confirm structure
    console.log('--- Sending Payload to API ---');
    console.log('Payload type:', typeof validatedPayload);
    console.log('Payload content:', JSON.stringify(validatedPayload, null, 2)); // Pretty print JSON
    console.log('-----------------------------');

    // Pass the validated (and already correctly structured) payload directly to the API
    // The 'post' function likely handles sending the object as JSON
    // Assuming the API returns the created patient object matching MedicalPatientDto
    return await post<MedicalPatientDto>('/patient/', validatedPayload);
  } catch (error) {
    if (error instanceof z.ZodError) {
      // Log detailed validation errors if Zod parsing fails
      console.error('Validation error in createPatient action:', error.errors);
      // You might want to throw a more specific error or return a structured error response
      throw new Error(
        `Validation failed: ${error.errors
          .map((e) => `${e.path.join('.')} - ${e.message}`)
          .join(', ')}`
      );
    }
    // Log other types of errors
    console.error('Error creating patient:', error);
    // Re-throw the error to be caught by the calling component (PatientCreate.tsx)
    throw error;
  }
}

// Define types specifically for the dashboard view
import { PatientListItem } from '../actions/getPatients';

// Sort options for the patient list
export type SortDirection = 'asc' | 'desc';
export type SortCriteria = 'name' | 'age-asc' | 'age-desc' | 'recent';

// Patient type with all required fields for the dashboard
export interface DashboardPatient {
  id: string;
  name: string;
  birthdate: string;
  gender: string;
  email: string;
  phone: string;
}

// Props for the patient table component
export interface PatientTableProps {
  patients: DashboardPatient[];
  onViewDetails: (patientId: string) => void;
  onViewClinicalHistory: (patientId: string) => void;
  onEditPatient: (patientId: string) => void;
  onDeletePatient: (patient: DashboardPatient) => void;
}

// Props for the patient actions dropdown
export interface PatientActionsProps {
  patient: DashboardPatient;
  onViewDetails: (patientId: string) => void;
  onViewClinicalHistory: (patientId: string) => void;
  onEditPatient: (patientId: string) => void;
  onDeletePatient: (patient: DashboardPatient) => void;
}

// Props for the search and filter component
export interface SearchAndFilterProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  sortBy: SortCriteria;
  onSortChange: (criteria: SortCriteria) => void;
  onAddPatient: () => void;
}

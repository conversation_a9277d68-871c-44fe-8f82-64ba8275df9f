'use client';
import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { getPatients } from '@/features/patients/actions/getPatients';
import { deletePatientTenant } from '@/features/patients/actions/deletePatientTenant';
import { ClinicalHistoryView } from '@/features/patients/components/ClinicalHistoryView';
import { PatientDetailsModal } from '@/features/patients/components/PatientDetailsModal';
import { EditPatientModal } from '@/features/patients/components/EditPatientModal';
import { SearchAndFilterBar } from '@/features/patients/components/SearchAndFilterBar';
import { PatientsTable } from '@/features/patients/components/PatientsTable';
import { DeletePatientDialog } from '@/features/patients/components/DeletePatientDialog';
import { PatientsPagination } from '@/features/patients/components/PatientsPagination';
import { usePatientFiltering } from '@/features/patients/hooks/usePatientFiltering';

import {
  DashboardPatient,
  SortCriteria,
  SortDirection
} from '@/features/patients/types/dashboardTypes';

const Page = () => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPatientId, setSelectedPatientId] = useState<string | null>(
    null
  );
  const [isClinicalHistoryOpen, setIsClinicalHistoryOpen] = useState(false);
  const [sortBy, setSortBy] = useState<SortCriteria>('name');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const [patients, setPatients] = useState<DashboardPatient[]>([]);
  const [isPatientDetailsOpen, setIsPatientDetailsOpen] = useState(false);
  const [selectedPatientForDetails, setSelectedPatientForDetails] = useState<
    string | null
  >(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [patientToDelete, setPatientToDelete] =
    useState<DashboardPatient | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [patientToEditId, setPatientToEditId] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [patientsPerPage] = useState(10);

  // Fetch patients data
  useEffect(() => {
    const fetchPatients = async () => {
      try {
        const result = await getPatients();
        setPatients(result.patients || []);
        if (!result.patients || result.patients.length === 0) {
          toast.info('No hay pacientes que mostrar');
        }
      } catch (error) {
        console.error('Error fetching patients:', error);
        toast.error('Error al cargar los pacientes');
        setPatients([]);
      }
    };

    fetchPatients();
  }, []);

  // Use the custom hook for filtering and sorting patients
  const { filteredAndSortedPatients } = usePatientFiltering({
    patients,
    searchTerm,
    sortBy,
    sortDirection
  });

  // Calculate pagination
  const totalPages = Math.ceil(
    filteredAndSortedPatients.length / patientsPerPage
  );
  const startIndex = (currentPage - 1) * patientsPerPage;
  const endIndex = startIndex + patientsPerPage;
  const paginatedPatients = filteredAndSortedPatients.slice(
    startIndex,
    endIndex
  );

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Handler functions
  const handleViewClinicalHistory = (patientId: string) => {
    setSelectedPatientId(patientId);
    setIsClinicalHistoryOpen(true);
  };

  const handleViewPatientDetails = (patientId: string) => {
    setSelectedPatientForDetails(patientId);
    setIsPatientDetailsOpen(true);
  };

  const handleSortChange = (criteria: SortCriteria) => {
    if (sortBy === criteria) {
      // Toggle direction if clicking the same criteria
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new criteria and reset direction to ascending
      setSortBy(criteria);
      setSortDirection('asc');
    }
  };

  const handleEditClick = (patientId: string) => {
    setPatientToEditId(patientId);
    setIsEditModalOpen(true);
  };

  const handleDeleteClick = (patient: DashboardPatient) => {
    setPatientToDelete(patient);
    setIsDeleteConfirmOpen(true);
  };

  const confirmDelete = useCallback(async () => {
    if (!patientToDelete) return;

    const patientId = patientToDelete.id;
    const patientName = patientToDelete.name;

    const promise = deletePatientTenant(patientId);

    toast.promise(promise, {
      loading: `Eliminando asociación de ${patientName}...`,
      success: (res) => {
        if (res.success) {
          // Optimistically remove patient from local state
          setPatients((prev) => prev.filter((p) => p.id !== patientId));
          setIsDeleteConfirmOpen(false);
          setPatientToDelete(null);
          return res.message || `${patientName} eliminado correctamente.`;
        } else {
          throw new Error(res.message || 'Error desconocido al eliminar.');
        }
      },
      error: (err) => {
        console.error('Deletion error:', err);
        setIsDeleteConfirmOpen(false);
        setPatientToDelete(null);
        return err.message || 'No se pudo eliminar la asociación del paciente.';
      }
    });
  }, [patientToDelete]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section with Search and Filters */}
      <SearchAndFilterBar
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        sortBy={sortBy}
        onSortChange={handleSortChange}
        onAddPatient={() => router.push('/dashboard/new-patient')}
      />

      {/* Table Section */}
      <div className="bg-white">
        <PatientsTable
          patients={paginatedPatients}
          onViewDetails={handleViewPatientDetails}
          onViewClinicalHistory={handleViewClinicalHistory}
          onEditPatient={handleEditClick}
          onDeletePatient={handleDeleteClick}
        />

        {/* Pagination */}
        {totalPages > 1 && (
          <PatientsPagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        )}
      </div>

      {/* Clinical History Dialog */}
      {selectedPatientId && (
        <ClinicalHistoryView
          patientId={selectedPatientId}
          isOpen={isClinicalHistoryOpen}
          onClose={() => setIsClinicalHistoryOpen(false)}
        />
      )}

      {/* Patient Details Modal */}
      {selectedPatientForDetails && (
        <PatientDetailsModal
          patientId={selectedPatientForDetails}
          isOpen={isPatientDetailsOpen}
          onClose={() => {
            setIsPatientDetailsOpen(false);
            setSelectedPatientForDetails(null);
          }}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <DeletePatientDialog
        isOpen={isDeleteConfirmOpen}
        onOpenChange={setIsDeleteConfirmOpen}
        patient={patientToDelete}
        onCancel={() => setPatientToDelete(null)}
        onConfirm={confirmDelete}
      />

      {/* Edit Patient Modal */}
      <EditPatientModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setPatientToEditId(null);
        }}
        patientId={patientToEditId}
      />
    </div>
  );
};

export default Page;

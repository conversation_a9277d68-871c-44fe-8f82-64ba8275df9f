'use client';
import { useState, useMemo, useEffect, useCallback } from 'react'; // Added useCallback
import Image from 'next/image';
import { Search, UserRoundPlus, ArrowUpDown, Edit, Trash2 } from 'lucide-react'; // Added Edit, Trash2
import Avatar from '@/features/patients/assets/AvarDefault.png';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator // Added Separator
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger // Although we trigger manually
} from '@/components/ui/alert-dialog';
import { ClinicalHistoryView } from '@/features/patients/components/ClinicalHistoryView';
import { toast } from 'sonner';
import {
  getPatients,
  PatientListItem
} from '@/features/patients/actions/getPatients'; // Corrected import path and added PatientListItem type
import { deletePatientTenant } from '@/features/patients/actions/deletePatientTenant'; // Added delete action
import { useRouter } from 'next/navigation';
import { PatientDetailsModal } from '@/features/patients/components/PatientDetailsModal';
import { EditPatientModal } from '@/features/patients/components/EditPatientModal'; // Import the Edit Modal

const Page = () => {
  const router = useRouter();
  const [isPatientFormOpen, setIsPatientFormOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPatientId, setSelectedPatientId] = useState<string | null>(
    null
  );
  const [isClinicalHistoryOpen, setIsClinicalHistoryOpen] = useState(false);
  const [sortBy, setSortBy] = useState<string>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [patients, setPatients] = useState<PatientListItem[]>([]); // Use specific type
  const [isPatientDetailsOpen, setIsPatientDetailsOpen] = useState(false);
  const [selectedPatientForDetails, setSelectedPatientForDetails] = useState<
    string | null
  >(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [patientToDelete, setPatientToDelete] = useState<any | null>(null); // Store patient object for context
  const [isEditModalOpen, setIsEditModalOpen] = useState(false); // State for edit modal
  const [patientToEditId, setPatientToEditId] = useState<string | null>(null); // State for patient being edited

  // Fetch patients data
  useEffect(() => {
    const fetchPatients = async () => {
      try {
        const result = await getPatients();
        setPatients(result.patients || []); // Set state with the patients array from the response
        if (!result.patients || result.patients.length === 0) {
          // Check the length of the patients array
          toast.info('No hay pacientes que mostrar');
        }
      } catch (error) {
        console.error('Error fetching patients:', error);
        toast.error('Error al cargar los pacientes');
        setPatients([]);
      }
    };

    fetchPatients();
  }, []); // Empty dependency array ensures this runs only once on mount

  // Filter and sort patients
  const filteredAndSortedPatients = useMemo(() => {
    // First filter by search term
    const filtered = patients.filter((patient) =>
      patient.name.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Then sort based on the selected criteria
    return [...filtered].sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'age-asc':
          // Convert birthdate strings to Date objects for comparison
          const dateA = new Date(a.birthdate);
          const dateB = new Date(b.birthdate);
          comparison = dateB.getTime() - dateA.getTime(); // Older people (earlier birthdates) first
          break;
        case 'age-desc':
          // Convert birthdate strings to Date objects for comparison
          const dateC = new Date(a.birthdate);
          const dateD = new Date(b.birthdate);
          comparison = dateC.getTime() - dateD.getTime(); // Younger people (later birthdates) first
          break;
        case 'gender':
          comparison = a.gender.localeCompare(b.gender);
          break;
        case 'recent':
          // For demo purposes, we'll just use the ID as a proxy for recency
          comparison = parseInt(b.id) - parseInt(a.id);
          break;
        default:
          comparison = a.name.localeCompare(b.name);
      }

      return sortDirection === 'asc' ? comparison : -comparison;
    });
  }, [patients, searchTerm, sortBy, sortDirection]);

  const handleViewClinicalHistory = (patientId: string) => {
    setSelectedPatientId(patientId);
    setIsClinicalHistoryOpen(true);
  };

  const handleViewPatientDetails = (patientId: string) => {
    setSelectedPatientForDetails(patientId);
    setIsPatientDetailsOpen(true);
  };

  const handleSort = (criteria: string) => {
    if (sortBy === criteria) {
      // Toggle direction if clicking the same criteria
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new criteria and reset direction to ascending
      setSortBy(criteria);
      setSortDirection('asc');
    }
  };

  // --- Handlers for Edit/Delete ---
  const handleEditClick = (patientId: string) => {
    // Set the patient ID to edit and open the modal
    setPatientToEditId(patientId);
    setIsEditModalOpen(true);
  };

  const handleDeleteClick = (patient: any) => {
    setPatientToDelete(patient);
    setIsDeleteConfirmOpen(true);
  };

  const confirmDelete = useCallback(async () => {
    if (!patientToDelete) return;

    const patientId = patientToDelete.id; // Assuming patient object has an id
    const patientName = patientToDelete.name; // For toast message

    const promise = deletePatientTenant(patientId);

    toast.promise(promise, {
      loading: `Eliminando asociación de ${patientName}...`,
      success: (res) => {
        if (res.success) {
          // Optimistically remove patient from local state
          setPatients((prev) => prev.filter((p) => p.id !== patientId));
          setIsDeleteConfirmOpen(false); // Close modal on success
          setPatientToDelete(null);
          return res.message || `${patientName} eliminado correctamente.`;
        } else {
          // Throw error to trigger the error state of the toast
          throw new Error(res.message || 'Error desconocido al eliminar.');
        }
      },
      error: (err) => {
        // Error is already handled by the toast
        console.error('Deletion error:', err);
        // Keep modal open on error? Or close? Let's close it.
        setIsDeleteConfirmOpen(false);
        setPatientToDelete(null);
        return err.message || 'No se pudo eliminar la asociación del paciente.';
      }
    });
  }, [patientToDelete]); // Dependency: patientToDelete

  // --- End Handlers ---

  return (
    <div className="">
      {/* Header Section */}
      <div className="grid grid-cols-[20%,60%,20%] items-center px-10 py-2">
        <div className="relative w-[90%]">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-500" />
          <input
            type="text"
            placeholder="Buscar"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full rounded-[5px] border-gray-300 bg-transparent py-2 pl-10"
          />
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger
            asChild
            className="flex items-center gap-2 rounded-[5px] border-gray-300"
          >
            <Button
              variant="outline"
              size="default"
              className="flex w-[200px] items-center gap-2 rounded-lg border-gray-300"
            >
              <ArrowUpDown />
              {sortBy === 'name' && 'Ordenar por nombre'}
              {sortBy === 'age-asc' && 'Mayor a menor edad'}
              {sortBy === 'age-desc' && 'Menor a mayor edad'}
              {sortBy === 'gender' && 'Ordenar por género'}
              {sortBy === 'recent' && 'Más recientes'}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent side="bottom" align="start" className="w-48">
            <DropdownMenuItem onSelect={() => handleSort('name')}>
              Alfabéticamente
            </DropdownMenuItem>
            <DropdownMenuItem onSelect={() => handleSort('age-asc')}>
              Mayor a menor edad
            </DropdownMenuItem>
            <DropdownMenuItem onSelect={() => handleSort('age-desc')}>
              Menor a mayor edad
            </DropdownMenuItem>
            <DropdownMenuItem onSelect={() => handleSort('gender')}>
              Por género
            </DropdownMenuItem>
            <DropdownMenuItem onSelect={() => handleSort('recent')}>
              Más recientes
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        <Button
          variant="outline"
          size="default"
          className="flex items-center gap-2 rounded-lg border-gray-300"
          onClick={() => router.push('/dashboard/new-patient')}
        >
          <UserRoundPlus className="text-gray-500" /> Agregar Paciente
        </Button>
      </div>

      {/* Table Section */}
      <Table className="w-full border-collapse border border-gray-300 px-10 py-2">
        <TableHeader>
          <TableRow className="grid grid-cols-[20%,15%,15%,20%,20%,10%] items-center border-b border-gray-300 bg-gray-100 px-10">
            <TableHead className="text-left">Nombre</TableHead>
            <TableHead className="text-left">Fecha de nacimiento</TableHead>
            <TableHead className="text-left">Género</TableHead>
            <TableHead className="text-left">Email</TableHead>
            <TableHead className="text-left">Teléfono</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredAndSortedPatients.map((patient, index) => (
            <TableRow
              key={index}
              className="grid grid-cols-[20%,15%,15%,20%,20%,10%] items-center border-b border-gray-200 px-10"
            >
              <TableCell className="flex items-center gap-[5px]">
                <Image
                  src={Avatar}
                  alt="Patient Avatar"
                  className="h-10 w-10 rounded-[5px]"
                />
                {patient.name}
              </TableCell>
              <TableCell>{patient.birthdate}</TableCell>
              <TableCell>{patient.gender}</TableCell>
              <TableCell>{patient.email}</TableCell>
              <TableCell>{patient.phone}</TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button className="text-blue-600">...</button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    side="bottom"
                    align="start"
                    className="w-48"
                  >
                    <DropdownMenuItem
                      onSelect={() => {
                        toast.info('Funcionalidad en desarrollo');
                        console.log('Agendar una consulta');
                      }}
                    >
                      Agendar una consulta
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onSelect={() => handleViewPatientDetails(patient.id)}
                    >
                      Ver datos personales
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onSelect={() => handleViewClinicalHistory(patient.id)}
                    >
                      Ver historial clínico
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onSelect={() => {
                        toast.info('Funcionalidad premium en desarrollo');
                        console.log('Chatear');
                      }}
                    >
                      Chatear
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onSelect={() => handleEditClick(patient.id)}
                      className="text-blue-600 hover:bg-blue-50"
                    >
                      <Edit className="mr-2 h-4 w-4" />
                      Editar Datos
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onSelect={() => handleDeleteClick(patient)} // Pass the whole patient object
                      className="text-red-600 hover:bg-red-50"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Eliminar Paciente
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Clinical History Dialog */}
      {selectedPatientId && (
        <ClinicalHistoryView
          patientId={selectedPatientId}
          isOpen={isClinicalHistoryOpen}
          onClose={() => setIsClinicalHistoryOpen(false)}
        />
      )}

      {/* Add the PatientDetailsModal */}
      {selectedPatientForDetails && (
        <PatientDetailsModal
          patientId={selectedPatientForDetails}
          isOpen={isPatientDetailsOpen}
          onClose={() => {
            setIsPatientDetailsOpen(false);
            setSelectedPatientForDetails(null);
          }}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={isDeleteConfirmOpen}
        onOpenChange={setIsDeleteConfirmOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>¿Estás seguro?</AlertDialogTitle>
            <AlertDialogDescription>
              Esta acción eliminará la asociación del paciente{' '}
              <strong>{patientToDelete?.name}</strong> con tu cuenta. Esta
              acción no se puede deshacer directamente aquí, pero el paciente
              podría ser re-asociado si es necesario.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setPatientToDelete(null)}>
              Cancelar
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Confirmar Eliminación
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Edit Patient Modal */}
      <EditPatientModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setPatientToEditId(null);
          // Optionally re-fetch patients list if update was successful
          // fetchPatients(); // Or rely on revalidatePath in the action
        }}
        patientId={patientToEditId}
      />
    </div>
  );
};

export default Page;

'use server';

import { get } from '../../../lib/api'; // Assuming lib/api.ts exists and exports get

// Define the structure for a single patient in the list
// Adjust based on the actual API response fields (e.g., firstName, lastName)
export interface PatientListItem {
  id: string;
  name: string; // Assuming a combined 'name' field or construct it if needed
  // Add other relevant fields if available (e.g., DNI, birthDate)
}

// Define the expected response structure from the API
// Adjust based on actual API pagination structure
export interface GetPatientsResponse {
  patients: PatientListItem[];
  total: number; // Total number of patients for pagination info
  // Add other pagination fields if provided (e.g., currentPage, pageSize)
}

/**
 * Fetches a list of patients with pagination.
 * @param page The page number to fetch (default: 1).
 * @param pageSize The number of patients per page (default: 100 for combobox).
 * @returns A promise resolving to the list of patients and total count.
 */
export async function getPatients(
  page: number = 1,
  pageSize: number = 100 // Fetch a decent number for the initial combobox list
): Promise<GetPatientsResponse> {
  try {
    const endpoint = `/patient?page=${page}&pageSize=${pageSize}`;
    console.log(`Fetching patients from: ${endpoint}`); // Log the endpoint being called
    const response = await get<GetPatientsResponse>(endpoint); // Using the imported get function

    // Basic validation - adjust based on your actual API response
    if (!response || !Array.isArray(response.patients)) {
      console.warn('Received unexpected format for patients list:', response);
      return { patients: [], total: 0 };
    }

    // Ensure names are strings (or handle potential null/undefined from API)
    // You might need to construct the name from firstName/lastName if 'name' isn't directly provided
    response.patients = response.patients.map((p) => ({
      ...p,
      id: String(p.id), // Ensure ID is a string
      name: String(p.name || 'Nombre no disponible') // Ensure name is a string
    }));

    console.log(`Successfully fetched ${response.patients.length} patients.`);
    return response;
  } catch (error) {
    console.error('Error fetching patients:', error);
    // Rethrow a more specific error or return a default error state
    if (error instanceof Error) {
      throw new Error(`Failed to fetch patients: ${error.message}`);
    }
    throw new Error('Failed to fetch patients due to an unexpected error.');
  }
}

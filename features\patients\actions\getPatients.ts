'use server';

import { get } from '../../../lib/api';
import { DashboardPatient } from '../types/dashboardTypes';

// Define the expected response structure from the API
export interface GetPatientsResponse {
  patients: DashboardPatient[];
  total: number;
}

/**
 * Fetches a list of patients with pagination.
 * @param page The page number to fetch (default: 1).
 * @param pageSize The number of patients per page (default: 100 for combobox).
 * @returns A promise resolving to the list of patients and total count.
 */
export async function getPatients(
  page: number = 1,
  pageSize: number = 100
): Promise<GetPatientsResponse> {
  try {
    const endpoint = `/patient?page=${page}&pageSize=${pageSize}`;
    console.log(`Fetching patients from: ${endpoint}`);
    const response = await get<any>(endpoint);

    // Basic validation
    if (!response || !Array.isArray(response.patients)) {
      console.warn('Received unexpected format for patients list:', response);
      return { patients: [], total: 0 };
    }

    // Transform the API response to match our DashboardPatient type
    const patients: DashboardPatient[] = response.patients.map((p: any) => ({
      id: String(p.id),
      name: String(
        p.name ||
          `${p.first_name || ''} ${p.last_name || ''}` ||
          'Nombre no disponible'
      ).trim(),
      birthdate: p.birthdate || p.birth_date || 'No disponible',
      gender: p.gender || 'No especificado',
      email: p.email || 'No disponible',
      phone: p.phone || 'No disponible'
    }));

    console.log(`Successfully fetched ${patients.length} patients.`);
    return {
      patients,
      total: response.total || patients.length
    };
  } catch (error) {
    console.error('Error fetching patients:', error);
    if (error instanceof Error) {
      throw new Error(`Failed to fetch patients: ${error.message}`);
    }
    throw new Error('Failed to fetch patients due to an unexpected error.');
  }
}

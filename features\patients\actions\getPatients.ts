'use server';

import { get } from '../../../lib/api';
import { DashboardPatient } from '../types/dashboardTypes';

// Define the expected response structure from the API
export interface GetPatientsResponse {
  patients: DashboardPatient[];
  total: number;
}

/**
 * Fetches a list of patients with pagination.
 * @param page The page number to fetch (default: 1).
 * @param pageSize The number of patients per page (default: 100 for combobox).
 * @returns A promise resolving to the list of patients and total count.
 */
export async function getPatients(
  page: number = 1,
  pageSize: number = 100
): Promise<GetPatientsResponse> {
  try {
    const endpoint = `/patient?page=${page}&pageSize=${pageSize}`;
    console.log(`Fetching patients from: ${endpoint}`);
    const response = await get<any>(endpoint);

    console.log('Raw API response:', response);

    // Handle different response formats
    let patientsArray: any[] = [];
    let total = 0;

    if (Array.isArray(response)) {
      // If response is directly an array
      patientsArray = response;
      total = response.length;
      console.log('Response is a direct array with', patientsArray.length, 'patients');
    } else if (response && Array.isArray(response.patients)) {
      // If response has a patients property
      patientsArray = response.patients;
      total = response.total || response.patients.length;
      console.log('Response has patients property with', patientsArray.length, 'patients');
    } else if (response && Array.isArray(response.items)) {
      // If response uses 'items' property (common in paginated APIs)
      patientsArray = response.items;
      total = response.meta?.totalItems || response.total || response.items.length;
      console.log('Response has items property with', patientsArray.length, 'patients');
    } else {
      console.warn('Received unexpected format for patients list:', response);
      return { patients: [], total: 0 };
    }

    // Transform the API response to match our DashboardPatient type
    const patients: DashboardPatient[] = patientsArray.map((p: any) => ({
      id: String(p.id),
      name: String(
        p.name ||
          `${p.first_name || ''} ${p.last_name || ''}` ||
          'Nombre no disponible'
      ).trim(),
      birthdate: p.birthdate || p.birth_date || 'No disponible',
      gender: p.gender || 'No especificado',
      email: p.email || 'No disponible',
      phone: p.phone || 'No disponible'
    }));

    console.log(`Successfully processed ${patients.length} patients.`);
    return {
      patients,
      total
    };
  } catch (error) {
    console.error('Error fetching patients:', error);
    if (error instanceof Error) {
      throw new Error(`Failed to fetch patients: ${error.message}`);
    }
    throw new Error('Failed to fetch patients due to an unexpected error.');
  }
}

'use client';

import { Search, UserRoundPlus, ArrowUpDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu';
import { SearchAndFilterProps, SortCriteria } from '../types/dashboardTypes';

export function SearchAndFilterBar({
  searchTerm,
  onSearchChange,
  sortBy,
  onSortChange,
  onAddPatient
}: SearchAndFilterProps) {
  const handleSort = (criteria: SortCriteria) => {
    onSortChange(criteria);
  };

  return (
    <div className="grid grid-cols-[20%,60%,20%] items-center px-10 py-2">
      <div className="relative w-[90%]">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-500" />
        <input
          type="text"
          placeholder="Buscar"
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="w-full rounded-[5px] border-gray-300 bg-transparent py-2 pl-10"
        />
      </div>
      <DropdownMenu>
        <DropdownMenuTrigger
          asChild
          className="flex items-center gap-2 rounded-[5px] border-gray-300"
        >
          <Button
            variant="outline"
            size="default"
            className="flex w-[200px] items-center gap-2 rounded-lg border-gray-300"
          >
            <ArrowUpDown />
            {sortBy === 'name' && 'Ordenar por nombre'}
            {sortBy === 'age-asc' && 'Mayor a menor edad'}
            {sortBy === 'age-desc' && 'Menor a mayor edad'}
            {sortBy === 'gender' && 'Ordenar por género'}
            {sortBy === 'recent' && 'Más recientes'}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent side="bottom" align="start" className="w-48">
          <DropdownMenuItem onSelect={() => handleSort('name')}>
            Alfabéticamente
          </DropdownMenuItem>
          <DropdownMenuItem onSelect={() => handleSort('age-asc')}>
            Mayor a menor edad
          </DropdownMenuItem>
          <DropdownMenuItem onSelect={() => handleSort('age-desc')}>
            Menor a mayor edad
          </DropdownMenuItem>
          <DropdownMenuItem onSelect={() => handleSort('gender')}>
            Por género
          </DropdownMenuItem>
          <DropdownMenuItem onSelect={() => handleSort('recent')}>
            Más recientes
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <Button
        variant="outline"
        size="default"
        className="flex items-center gap-2 rounded-lg border-gray-300"
        onClick={onAddPatient}
      >
        <UserRoundPlus className="text-gray-500" /> Agregar Paciente
      </Button>
    </div>
  );
}

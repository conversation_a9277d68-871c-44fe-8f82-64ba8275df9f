'use client';

import { Search, UserRoundPlus, ArrowUpDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu';
import { SearchAndFilterProps, SortCriteria } from '../types/dashboardTypes';

export function SearchAndFilterBar({
  searchTerm,
  onSearchChange,
  sortBy,
  onSortChange,
  onAddPatient
}: SearchAndFilterProps) {
  const handleSort = (criteria: SortCriteria) => {
    onSortChange(criteria);
  };

  return (
    <div className="flex items-center justify-between px-6 py-4 bg-white border-b border-gray-200">
      {/* Search Input */}
      <div className="relative w-80">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400 h-4 w-4" />
        <input
          type="text"
          placeholder="Buscar"
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="w-full rounded-lg border border-gray-300 bg-white py-2 pl-10 pr-4 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
        />
      </div>

      {/* Center - Sort Dropdown */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="default"
            className="flex items-center gap-2 rounded-lg border-gray-300 bg-white hover:bg-gray-50"
          >
            <ArrowUpDown className="h-4 w-4" />
            Ordenar
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent side="bottom" align="center" className="w-48">
          <DropdownMenuItem onSelect={() => handleSort('name')}>
            Alfabéticamente
          </DropdownMenuItem>
          <DropdownMenuItem onSelect={() => handleSort('age-asc')}>
            Mayor a menor edad
          </DropdownMenuItem>
          <DropdownMenuItem onSelect={() => handleSort('age-desc')}>
            Menor a mayor edad
          </DropdownMenuItem>
          <DropdownMenuItem onSelect={() => handleSort('recent')}>
            Más recientes
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Add Patient Button */}
      <Button
        variant="outline"
        size="default"
        className="flex items-center gap-2 rounded-lg border-gray-300 bg-white hover:bg-gray-50"
        onClick={onAddPatient}
      >
        <UserRoundPlus className="h-4 w-4 text-gray-500" />
        Agregar paciente
      </Button>
    </div>
  );
}

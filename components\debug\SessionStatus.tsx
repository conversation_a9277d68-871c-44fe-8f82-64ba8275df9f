'use client';

import { useState, useEffect } from 'react';
import { checkSession, getSessionDebugInfo, SessionInfo } from '@/features/auth/actions/checkSession';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export function SessionStatus() {
  const [sessionInfo, setSessionInfo] = useState<SessionInfo | null>(null);
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const checkSessionStatus = async () => {
    setIsLoading(true);
    try {
      const [session, debug] = await Promise.all([
        checkSession(),
        getSessionDebugInfo()
      ]);
      setSessionInfo(session);
      setDebugInfo(debug);
    } catch (error) {
      console.error('Error checking session:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    checkSessionStatus();
  }, []);

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Estado de Sesión
          <Button 
            onClick={checkSessionStatus} 
            disabled={isLoading}
            size="sm"
            variant="outline"
          >
            {isLoading ? 'Verificando...' : 'Verificar'}
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {sessionInfo && (
          <div>
            <div className={`inline-flex items-center px-2 py-1 rounded-full text-sm font-medium ${
              sessionInfo.isValid 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              {sessionInfo.isValid ? '✅ Sesión Activa' : '❌ Sesión Inválida'}
            </div>
            
            {sessionInfo.user && (
              <div className="mt-2 text-sm text-gray-600">
                <p><strong>Usuario:</strong> {sessionInfo.user.name}</p>
                <p><strong>Email:</strong> {sessionInfo.user.email}</p>
                <p><strong>Rol:</strong> {sessionInfo.user.role}</p>
                <p><strong>Tenant ID:</strong> {sessionInfo.user.tenant_id}</p>
              </div>
            )}
          </div>
        )}

        {debugInfo && (
          <div className="text-xs text-gray-500 border-t pt-2">
            <p><strong>Debug Info:</strong></p>
            <p>Token presente: {debugInfo.hasToken ? 'Sí' : 'No'}</p>
            <p>Longitud token: {debugInfo.tokenLength}</p>
            <p>Tenant ID presente: {debugInfo.hasTenantId ? 'Sí' : 'No'}</p>
            {debugInfo.tokenPreview && (
              <p>Token preview: {debugInfo.tokenPreview}</p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

'use client';

import { useEffect, useState, useRef } from 'react'; // Added useState, useRef
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { But<PERSON> } from '../../../components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '../../../components/ui/form';
import { Input } from '../../../components/ui/input';
import { Card, CardContent } from '../../../components/ui/card';
import {
  Avatar,
  AvatarFallback,
  AvatarImage
} from '../../../components/ui/avatar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../../../components/ui/select';
import { cn } from '../../../lib/utils';
import { createPatient, CreatePatientPayload } from '../actions/createPatient'; // Import CreatePatientPayload type
import { updatePatient } from '../actions/updatePatient'; // Added update action
import { MedicalPatientDto, UpdatePatientPayloadDto } from '../types/patient'; // Added types
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

const formSchema = z.object({
  photo: z.string().optional(),
  name: z.string().min(2, {
    message: 'El nombre debe tener al menos 2 caracteres.'
  }),
  lastName: z.string().min(2, {
    message: 'El apellido debe tener al menos 2 caracteres.'
  }),
  email: z.string().email({
    message: 'Por favor ingrese un email válido.'
  }),
  dni: z
    .string()
    .min(7, {
      message: 'El DNI debe tener al menos 7 caracteres.'
    })
    .max(9),
  birthDate: z.string(),
  nationality: z.string().default('Argentina'),
  gender: z.string(),
  phonePrefix: z.string().default('54'),
  phone: z.string().min(8, {
    message: 'El teléfono debe tener al menos 8 dígitos.'
  }),
  direction: z.string(),
  country: z.string().default('Argentina'),
  province: z.string(),
  city: z.string(),
  postalCode: z.string(),
  directionNumber: z.string().min(3, {
    message: 'El número debe tener al menos 3 dígitos.'
  }),
  apartment: z.string().optional(),
  healthCareNumber: z.string().optional()
});

interface PatientCreateProps {
  isEditMode?: boolean;
  patientId?: string;
  initialData?: MedicalPatientDto | null; // Allow null or undefined
  onClose?: () => void; // Optional callback for closing modal/view
}

const PatientCreate: React.FC<PatientCreateProps> = ({
  isEditMode = false,
  patientId,
  initialData,
  onClose
}) => {
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  // Function to format ISO date string to YYYY-MM-DD for input type="date"
  const formatDateForInput = (isoDate?: string | null) => {
    if (!isoDate) return '';
    try {
      return isoDate.split('T')[0];
    } catch (e) {
      console.error('Error formatting date:', isoDate, e);
      return '';
    }
  };

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues:
      isEditMode && initialData
        ? {
            name: initialData.user.name || '',
            lastName: initialData.user.last_name || '',
            email: initialData.user.email || '',
            dni: initialData.user.dni || '',
            birthDate: formatDateForInput(initialData.user.birth_date),
            nationality: initialData.user.nationality || 'Argentina',
            gender: initialData.user.gender || '',
            phonePrefix: initialData.user.phone_prefix || '54',
            phone: initialData.user.phone || '',
            photo: initialData.user.image || '', // Keep initial photo URL from data
            direction: initialData.patient.direction || '',
            country: initialData.patient.country || 'Argentina',
            province: initialData.patient.province || '',
            city: initialData.patient.city || '',
            postalCode: initialData.patient.postal_code || '',
            directionNumber: initialData.patient.direction_number || '',
            apartment: initialData.patient.apartment || '',
            healthCareNumber: initialData.patient.health_care_number || ''
          }
        : {
            // Default values for create mode
            country: 'Argentina',
            nationality: 'Argentina',
            phonePrefix: '54',
            name: '',
            lastName: '',
            email: '',
            dni: '',
            birthDate: '',
            gender: '',
            phone: '',
            photo: '',
            direction: '',
            province: '',
            city: '',
            postalCode: '',
            directionNumber: '',
            apartment: '',
            healthCareNumber: ''
          }
  });

  // Reset form if initialData changes (useful if used in a modal that re-renders)
  useEffect(() => {
    if (isEditMode && initialData) {
      form.reset({
        name: initialData.user.name || '',
        lastName: initialData.user.last_name || '',
        email: initialData.user.email || '',
        dni: initialData.user.dni || '',
        birthDate: formatDateForInput(initialData.user.birth_date),
        nationality: initialData.user.nationality || 'Argentina',
        gender: initialData.user.gender || '',
        phonePrefix: initialData.user.phone_prefix || '54',
        phone: initialData.user.phone || '',
        photo: initialData.user.image || '', // Keep initial photo URL from data
        direction: initialData.patient.direction || '',
        country: initialData.patient.country || 'Argentina',
        province: initialData.patient.province || '',
        city: initialData.patient.city || '',
        postalCode: initialData.patient.postal_code || '',
        directionNumber: initialData.patient.direction_number || '',
        apartment: initialData.patient.apartment || '',
        healthCareNumber: initialData.patient.health_care_number || ''
      });
    } else if (!isEditMode) {
      form.reset({
        // Reset to create defaults if switching mode
        country: 'Argentina',
        nationality: 'Argentina',
        phonePrefix: '54',
        name: '',
        lastName: '',
        email: '',
        dni: '',
        birthDate: '',
        gender: '',
        phone: '',
        photo: '', // Keep photo field for potential URL
        direction: '',
        province: '',
        city: '',
        postalCode: '',
        directionNumber: '',
        apartment: '',
        healthCareNumber: ''
      });
    }
  }, [initialData, isEditMode, form]); // Use form directly instead of form.reset

  // Effect to set initial preview if editing with an existing image URL
  useEffect(() => {
    if (isEditMode && initialData?.user.image) {
      setPreviewUrl(initialData.user.image);
      setSelectedFile(null); // Ensure no file is selected initially in edit mode
    } else if (!isEditMode) {
      setPreviewUrl(null); // Clear preview in create mode
      setSelectedFile(null);
    }
  }, [isEditMode, initialData]);

  // Effect to revoke object URL on unmount or when file changes
  useEffect(() => {
    let objectUrl: string | null = null;
    if (selectedFile) {
      objectUrl = URL.createObjectURL(selectedFile);
      setPreviewUrl(objectUrl);
    }

    return () => {
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl);
        setPreviewUrl(null); // Clear preview when file changes or unmounts
      }
    };
  }, [selectedFile]);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      setSelectedFile(file);
      // The preview URL is set via the useEffect hook watching selectedFile
      // Optionally update form state if needed, but 'photo' field expects URL
      // form.setValue('photo', file.name); // Example: set filename, but API expects URL
    } else {
      setSelectedFile(null);
      toast.error('Por favor seleccione un archivo de imagen válido.');
    }
    // Reset file input value to allow selecting the same file again
    if (event.target) {
      event.target.value = '';
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleDeletePhoto = () => {
    setSelectedFile(null);
    setPreviewUrl(isEditMode ? initialData?.user.image || null : null); // Revert to initial image or null
    form.setValue('photo', isEditMode ? initialData?.user.image || '' : ''); // Reset form field value
    if (fileInputRef.current) {
      fileInputRef.current.value = ''; // Clear the file input
    }
  };

  async function onSubmit(values: z.infer<typeof formSchema>) {
    let promise: Promise<any>; // Promise type will be determined below

    if (isEditMode) {
      // --- EDIT MODE ---
      const updatePayload: UpdatePatientPayloadDto = { user: {}, patient: {} };

      // Populate user fields that changed
      if (values.name !== initialData?.user.name)
        updatePayload.user!.name = values.name;
      if (values.lastName !== initialData?.user.last_name)
        updatePayload.user!.last_name = values.lastName;
      if (values.email !== initialData?.user.email)
        updatePayload.user!.email = values.email;
      if (values.dni !== initialData?.user.dni)
        updatePayload.user!.dni = values.dni;
      const formattedInitialBirthDate = formatDateForInput(
        initialData?.user.birth_date
      );
      if (values.birthDate && values.birthDate !== formattedInitialBirthDate) {
        updatePayload.user!.birth_date = new Date(
          values.birthDate
        ).toISOString();
      } else if (!values.birthDate && formattedInitialBirthDate) {
        // Handle clearing the date if applicable by API (might need null or specific value)
        // updatePayload.user!.birth_date = null; // Example if API accepts null to clear
      }
      if (values.nationality !== initialData?.user.nationality)
        updatePayload.user!.nationality = values.nationality;
      if (values.gender !== initialData?.user.gender)
        updatePayload.user!.gender = values.gender;
      if (values.phonePrefix !== initialData?.user.phone_prefix)
        updatePayload.user!.phone_prefix = values.phonePrefix;
      if (values.phone !== initialData?.user.phone)
        updatePayload.user!.phone = values.phone;
      if ((values.photo || null) !== (initialData?.user.image || null))
        if ((values.photo || null) !== (initialData?.user.image || null))
          // IMPORTANT: Sending the actual file is not handled here.
          // The 'image' field in the payload still uses values.photo (expected URL).
          // A separate upload mechanism is required to handle 'selectedFile'.
          updatePayload.user!.image = values.photo || null; // Send updated URL or null

      // Populate patient fields that changed
      if (values.direction !== initialData?.patient.direction)
        updatePayload.patient!.direction = values.direction;
      if (values.country !== initialData?.patient.country)
        updatePayload.patient!.country = values.country;
      if (values.province !== initialData?.patient.province)
        updatePayload.patient!.province = values.province;
      if (values.city !== initialData?.patient.city)
        updatePayload.patient!.city = values.city;
      if (values.postalCode !== initialData?.patient.postal_code)
        updatePayload.patient!.postal_code = values.postalCode;
      // Use undefined for empty optional fields in PATCH payload
      if (
        values.directionNumber !== (initialData?.patient.direction_number ?? '')
      ) {
        // Compare with empty string if initial is null/undefined
        updatePayload.patient!.direction_number =
          values.directionNumber || undefined; // Send undefined if empty
      }
      if (
        (values.apartment || undefined) !==
        (initialData?.patient.apartment || undefined)
      ) {
        // Compare with undefined
        updatePayload.patient!.apartment = values.apartment || undefined; // Send undefined if empty
      }
      if (
        (values.healthCareNumber || undefined) !==
        (initialData?.patient.health_care_number || undefined)
      ) {
        // Compare with undefined
        updatePayload.patient!.health_care_number =
          values.healthCareNumber || undefined; // Send undefined if empty
      }

      // Clean empty objects
      if (Object.keys(updatePayload.user!).length === 0)
        delete updatePayload.user;
      if (Object.keys(updatePayload.patient!).length === 0)
        delete updatePayload.patient;

      // If no changes, inform user and return
      if (!updatePayload.user && !updatePayload.patient) {
        toast.info('No se detectaron cambios.');
        if (onClose) onClose();
        return;
      }

      promise = updatePatient(patientId!, updatePayload);
    } else {
      // --- CREATE MODE ---
      const createPayload: CreatePatientPayload = {
        user: {
          name: values.name,
          last_name: values.lastName,
          email: values.email,
          dni: values.dni,
          birth_date: values.birthDate
            ? new Date(values.birthDate).toISOString()
            : '', // Send empty string or handle as needed by API if date is required
          nationality: values.nationality,
          gender: values.gender,
          phone_prefix: values.phonePrefix,
          phone: values.phone,
          // IMPORTANT: Sending the actual file is not handled here.
          // The 'image' field uses values.photo (expected URL).
          // A separate upload mechanism is required to handle 'selectedFile'.
          image: values.photo || null // Send URL or null
        },
        patient: {
          direction: values.direction,
          country: values.country,
          province: values.province,
          city: values.city,
          postal_code: values.postalCode,
          direction_number: values.directionNumber || null, // createPatient schema allows null
          apartment: values.apartment || null, // createPatient schema allows null
          health_care_number: values.healthCareNumber || null // createPatient schema allows null
        }
      };
      // Validate birth_date for create mode if it's required
      if (!createPayload.user.birth_date) {
        toast.error('La fecha de nacimiento es requerida.');
        return; // Stop submission if required field is missing
      }

      promise = createPatient(createPayload);
    }

    // --- Execute Promise with Toast ---
    // Note: 'promise' was already declared at the start of the function
    toast.promise(promise, {
      loading: isEditMode ? 'Actualizando paciente...' : 'Creando paciente...',
      success: (result) => {
        // Handle different success responses based on the mode
        let successMessage = '';
        if (isEditMode) {
          // updatePatient returns { success: boolean, message?: string, data?: MedicalPatientDto }
          const updateResult = result as {
            success: boolean;
            message?: string;
            data?: MedicalPatientDto;
          };
          if (updateResult.success) {
            successMessage =
              updateResult.message || 'Paciente actualizado exitosamente';
          } else {
            // Throw error to trigger toast's error state
            throw new Error(updateResult.message || 'Error al actualizar.');
          }
        } else {
          // createPatient returns MedicalPatientDto on success (or throws on error)
          const createResult = result as MedicalPatientDto;
          // Basic check if the result looks like a patient object
          if (createResult && createResult.user && createResult.patient) {
            successMessage = 'Paciente creado exitosamente';
          } else {
            // If API returns something else on success, adjust this check
            console.warn(
              'Create patient success response format unexpected:',
              result
            );
            // Assume success if no error was thrown by createPatient action
            successMessage = 'Paciente creado (respuesta inesperada)';
            // Or throw an error if a specific structure is absolutely required
            // throw new Error('Respuesta inesperada al crear paciente.');
          }
        }

        if (onClose) onClose(); // Close modal on success
        else router.push('/dashboard'); // Redirect if not in modal context
        return successMessage; // Return the message for the toast
      },
      error: (err) => {
        // Error is already an instance of Error (thrown by actions or the success handler)
        console.error(
          isEditMode
            ? 'Error al actualizar paciente:'
            : 'Error al crear paciente:',
          err
        );
        return (
          err.message ||
          (isEditMode
            ? 'Error al actualizar el paciente'
            : 'Error al crear el paciente')
        );
      }
    });
  }

  return (
    <div className="flex-1 p-6">
      <div className="mb-6 flex items-center gap-2">
        {/* Show back button only if not in edit mode (assuming edit is in a modal) */}
        {!isEditMode && (
          <Button variant="outline" onClick={() => router.back()}>
            Regresar
          </Button>
        )}
        {/* Conditionally render title */}
        <h1 className="text-2xl font-semibold">
          {isEditMode ? 'Editar Paciente' : 'Agregar Paciente'}
        </h1>
      </div>

      <Card className="mb-6">
        <CardContent className="p-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="flex gap-8">
              {/* Photo Upload */}
              <div className="flex w-64 flex-col items-center gap-4">
                {/* Hidden File Input */}
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileSelect}
                  accept="image/*" // Only accept image files
                  style={{ display: 'none' }} // Hide the default input
                />
                <Avatar
                  className="h-48 w-48 cursor-pointer"
                  onClick={handleUploadClick}
                >
                  <AvatarImage src={previewUrl ?? undefined} />{' '}
                  {/* Use previewUrl state */}
                  <AvatarFallback>PA</AvatarFallback>
                </Avatar>
                <div className="flex w-full flex-col gap-2">
                  <Button
                    variant="outline"
                    type="button"
                    className="flex-1 bg-[#487FFA] text-white hover:bg-[#487FFA]/90"
                    onClick={handleUploadClick} // Trigger file input click
                  >
                    Subir foto
                  </Button>
                  <Button
                    variant="outline"
                    type="button"
                    className="flex-1 border-red-500 text-red-500 hover:bg-red-50"
                    onClick={handleDeletePhoto} // Clear selection
                    disabled={!previewUrl && !selectedFile} // Disable if no photo/preview
                  >
                    Eliminar
                  </Button>
                </div>
              </div>

              {/* Form Fields */}
              <div className="flex-1 space-y-8">
                {/* Personal Information */}
                <div>
                  <div className="grid grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Nombre(s)
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Ingrese el nombre"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="lastName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Apellido(s)
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Ingrese el apellido"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="birthDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Fecha de nacimiento
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="date"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="gender"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">Sexo</FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Seleccione el sexo" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Masculino">
                                  Masculino
                                </SelectItem>
                                <SelectItem value="Femenino">
                                  Femenino
                                </SelectItem>
                                <SelectItem value="Otro">Otro</SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Correo electrónico
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="Ingrese el email"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="dni"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">DNI</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Ingrese el DNI"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="phonePrefix"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Prefijo telefónico
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Ej: 54"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Teléfono
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Ingrese el número"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Address Information */}
                <div>
                  <div className="grid grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="direction"
                      render={({ field }) => (
                        <FormItem className="col-span-2">
                          <FormLabel className="text-[#808080]">
                            Dirección
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Ingrese la dirección"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="country"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">País</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Seleccione el país"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="province"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Provincia/estado
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Seleccione la provincia"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="city"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Ciudad
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Ingrese la ciudad"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="postalCode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Código Postal
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Ingrese el código postal"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="directionNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Número
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Ingrese el número"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="apartment"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Departamento
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Ingrese el departamento"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="healthCareNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#808080]">
                            Número de Obra Social
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Ingrese el número"
                              className="border-[#DCDBDB]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <div className="flex justify-end pt-4">
                  <Button type="submit">
                    {isEditMode ? 'Actualizar Paciente' : 'Guardar Paciente'}
                  </Button>
                </div>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

export default PatientCreate;

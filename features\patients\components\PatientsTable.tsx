'use client';

import Image from 'next/image';
import Avatar from '@/features/patients/assets/AvarDefault.png';
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell
} from '@/components/ui/table';
import { PatientActions } from './PatientActions';
import { PatientTableProps } from '../types/dashboardTypes';

export function PatientsTable({
  patients,
  onViewDetails,
  onViewClinicalHistory,
  onEditPatient,
  onDeletePatient
}: PatientTableProps) {
  return (
    <div className="w-full">
      <Table className="w-full">
        <TableHeader>
          <TableRow className="border-b border-gray-200 bg-gray-50">
            <TableHead className="text-left font-medium text-gray-600 py-3 px-6">
              Nombre
            </TableHead>
            <TableHead className="text-left font-medium text-gray-600 py-3 px-6">
              Fecha de nacimiento
            </TableHead>
            <TableHead className="text-left font-medium text-gray-600 py-3 px-6">
              G<PERSON>ero
            </TableHead>
            <TableHead className="text-left font-medium text-gray-600 py-3 px-6">
              Email
            </TableHead>
            <TableHead className="text-left font-medium text-gray-600 py-3 px-6">
              Teléfono
            </TableHead>
            <TableHead className="w-12"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {patients.map((patient, index) => (
            <TableRow
              key={patient.id}
              className="border-b border-gray-100 hover:bg-gray-50 transition-colors"
            >
              <TableCell className="py-4 px-6">
                <div className="flex items-center gap-3">
                  <Image
                    src={Avatar}
                    alt="Patient Avatar"
                    className="h-10 w-10 rounded-lg object-cover"
                  />
                  <span className="font-medium text-gray-900">
                    {patient.name}
                  </span>
                </div>
              </TableCell>
              <TableCell className="py-4 px-6 text-gray-600">
                {patient.birthdate}
              </TableCell>
              <TableCell className="py-4 px-6 text-gray-600">
                {patient.gender}
              </TableCell>
              <TableCell className="py-4 px-6 text-gray-600">
                {patient.email}
              </TableCell>
              <TableCell className="py-4 px-6 text-gray-600">
                {patient.phone}
              </TableCell>
              <TableCell className="py-4 px-6">
                <PatientActions
                  patient={patient}
                  onViewDetails={onViewDetails}
                  onViewClinicalHistory={onViewClinicalHistory}
                  onEditPatient={onEditPatient}
                  onDeletePatient={onDeletePatient}
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}

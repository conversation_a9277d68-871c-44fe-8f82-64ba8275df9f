/**
 * Script para probar la funcionalidad del dashboard de pacientes
 * Este script verifica:
 * 1. Funcionalidad del buscador
 * 2. Funcionalidad del ordenamiento
 * 3. Persistencia de la sesión
 */

import { DashboardPatient } from '@/features/patients/types/dashboardTypes';

// Mock data para pruebas
const mockPatients: DashboardPatient[] = [
  {
    id: '1',
    name: '<PERSON> Baglivo',
    birthdate: '01/01/2000',
    gender: '<PERSON><PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '**********'
  },
  {
    id: '2',
    name: '<PERSON>',
    birthdate: '15/03/1985',
    gender: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '**********'
  },
  {
    id: '3',
    name: '<PERSON>',
    birthdate: '22/07/1990',
    gender: '<PERSON><PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '**********'
  },
  {
    id: '4',
    name: '<PERSON>',
    birthdate: '08/12/1978',
    gender: 'Femenino',
    email: '<EMAIL>',
    phone: '**********'
  },
  {
    id: '5',
    name: '<PERSON>',
    birthdate: '30/05/1995',
    gender: 'Masculino',
    email: '<EMAIL>',
    phone: '**********'
  }
];

/**
 * Función para probar el filtrado por nombre
 */
function testSearchFunctionality() {
  console.log('🔍 Probando funcionalidad de búsqueda...');
  
  const searchTerms = ['Santiago', 'Ana', 'Carlos', 'xyz', ''];
  
  searchTerms.forEach(term => {
    const filtered = mockPatients.filter(patient =>
      patient.name.toLowerCase().includes(term.toLowerCase())
    );
    
    console.log(`Búsqueda: "${term}" -> ${filtered.length} resultados`);
    filtered.forEach(patient => {
      console.log(`  - ${patient.name}`);
    });
  });
}

/**
 * Función para probar el ordenamiento
 */
function testSortingFunctionality() {
  console.log('\n📊 Probando funcionalidad de ordenamiento...');
  
  // Ordenamiento alfabético
  const sortedByName = [...mockPatients].sort((a, b) => 
    a.name.localeCompare(b.name)
  );
  console.log('Ordenamiento alfabético:');
  sortedByName.forEach(patient => {
    console.log(`  - ${patient.name}`);
  });
  
  // Ordenamiento por edad (mayor a menor)
  const sortedByAgeDesc = [...mockPatients].sort((a, b) => {
    const dateA = new Date(a.birthdate.split('/').reverse().join('-'));
    const dateB = new Date(b.birthdate.split('/').reverse().join('-'));
    return dateB.getTime() - dateA.getTime(); // Más viejos primero
  });
  console.log('\nOrdenamiento por edad (mayor a menor):');
  sortedByAgeDesc.forEach(patient => {
    console.log(`  - ${patient.name} (${patient.birthdate})`);
  });
  
  // Ordenamiento por edad (menor a mayor)
  const sortedByAgeAsc = [...mockPatients].sort((a, b) => {
    const dateA = new Date(a.birthdate.split('/').reverse().join('-'));
    const dateB = new Date(b.birthdate.split('/').reverse().join('-'));
    return dateA.getTime() - dateB.getTime(); // Más jóvenes primero
  });
  console.log('\nOrdenamiento por edad (menor a mayor):');
  sortedByAgeAsc.forEach(patient => {
    console.log(`  - ${patient.name} (${patient.birthdate})`);
  });
}

/**
 * Función para probar la paginación
 */
function testPaginationFunctionality() {
  console.log('\n📄 Probando funcionalidad de paginación...');
  
  const patientsPerPage = 2;
  const totalPages = Math.ceil(mockPatients.length / patientsPerPage);
  
  console.log(`Total de pacientes: ${mockPatients.length}`);
  console.log(`Pacientes por página: ${patientsPerPage}`);
  console.log(`Total de páginas: ${totalPages}`);
  
  for (let page = 1; page <= totalPages; page++) {
    const startIndex = (page - 1) * patientsPerPage;
    const endIndex = startIndex + patientsPerPage;
    const paginatedPatients = mockPatients.slice(startIndex, endIndex);
    
    console.log(`\nPágina ${page}:`);
    paginatedPatients.forEach(patient => {
      console.log(`  - ${patient.name}`);
    });
  }
}

/**
 * Función principal para ejecutar todas las pruebas
 */
function runTests() {
  console.log('🧪 Iniciando pruebas de funcionalidad del dashboard...\n');
  
  testSearchFunctionality();
  testSortingFunctionality();
  testPaginationFunctionality();
  
  console.log('\n✅ Todas las pruebas completadas!');
}

// Ejecutar las pruebas si el script se ejecuta directamente
if (typeof window === 'undefined') {
  runTests();
}

export {
  testSearchFunctionality,
  testSortingFunctionality,
  testPaginationFunctionality,
  runTests
};
